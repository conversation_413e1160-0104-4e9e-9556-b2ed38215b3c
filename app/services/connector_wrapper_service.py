from typing import Any, Dict

import grpc
import structlog

from app.grpc_ import connector_pb2, connector_pb2_grpc
from app.modules.connectors.handlers.gdrive.services.google_drive_grpc_service import (
    GoogleDriveGrpcService,
)

# from app.modules.connectors.handlers.gdrive.workers.sync_worker import (
#     GoogleDriveSyncWorker,
# )
from app.modules.connectors.workers.tasks import sync_drive_task
from app.modules.organisation.services.organisation import OrganisationService
from app.modules.organisation.services.source import SourceService

logger = structlog.get_logger()


def convert_properties_to_string_map(properties):
    """
    Convert a properties dictionary to a string-only map suitable for protobuf.

    Args:
        properties (dict): Dictionary with mixed value types

    Returns:
        dict: Dictionary with all values converted to strings
    """
    if not properties or not isinstance(properties, dict):
        return {}

    string_properties = {}
    for key, value in properties.items():
        # Convert key to string if needed
        str_key = str(key) if not isinstance(key, str) else key

        # Convert value to string based on type
        if isinstance(value, bool):
            str_value = "true" if value else "false"
        elif value is None:
            str_value = ""
        elif isinstance(value, (int, float)):
            str_value = str(value)
        elif isinstance(value, str):
            str_value = value
        else:
            # For complex objects, convert to string representation
            str_value = str(value)

        string_properties[str_key] = str_value

    return string_properties


class ConnectorWrapperService(connector_pb2_grpc.ConnectorServiceServicer):
    """
    Wrapper service for connector operations.
    This service handles all connector-related gRPC calls and delegates to appropriate handlers.
    """

    def __init__(self):
        self.source_service = SourceService()
        # self.sync_worker = GoogleDriveSyncWorker()
        self.organisation_service = OrganisationService()
        self.google_drive_service = GoogleDriveGrpcService()
        logger.info("ConnectorWrapperService initialized")

    def addSource(self, request, context):
        """Add a new source with credentials"""
        try:
            logger.info(
                "Adding new source",
                source_type=request.type,
                organisation_id=request.organisation_id,
                name=request.name,
            )

            # Delegate to the appropriate service based on source type
            if request.type == connector_pb2.GOOGLE_DRIVE:
                # Create a mock request object that matches what SourceService expects
                class MockRequest:
                    def __init__(self, organisation_id, name, key, file_ids=None):
                        self.organisation_id = organisation_id
                        self.name = name
                        self.key = key
                        self.file_ids = file_ids or []

                mock_request = MockRequest(
                    organisation_id=request.organisation_id,
                    name=request.name,
                    key=request.key,
                )

                # Use existing SourceService logic
                result = self.source_service.addGoogleDriveSource(mock_request, context)

                # Convert organisation_pb2 response to connector_pb2 response
                if result.success:
                    # Create SourceModel for connector response
                    source_model = connector_pb2.SourceModel(
                        id=result.source.id,
                        organisation_id=result.source.organisation_id,
                        type=request.type,
                        name=result.source.name,
                        status="active" if result.success else "inactive",
                        created_at=result.source.created_at,
                        updated_at=result.source.updated_at,
                        connector_type=connector_pb2.UNSTRUCTURED,  # Google Drive is unstructured
                        last_sync_at="",
                    )

                    # Convert synced files
                    synced_files = []
                    for file_info in result.synced_files:
                        drive_file = connector_pb2.DriveFileModel(
                            id=file_info.id,
                            name=file_info.name,
                            mime_type="",
                            size=0,
                            created_time="",
                            modified_time="",
                            web_view_link="",
                            permissions={},
                        )
                        synced_files.append(drive_file)

                    return connector_pb2.AddSourceResponse(
                        success=True,
                        message=result.message,
                        source=source_model,
                        synced_files=synced_files,
                    )
                else:
                    return connector_pb2.AddSourceResponse(
                        success=False, message=result.message
                    )
            else:
                return connector_pb2.AddSourceResponse(
                    success=False, message=f"Unsupported source type: {request.type}"
                )

        except Exception as e:
            logger.error("Error adding source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return connector_pb2.AddSourceResponse(
                success=False, message=f"Error adding source: {str(e)}"
            )

    # def listSources(self, request, context):
    #     """List sources for an organisation"""
    #     try:
    #         logger.info("Listing sources", organisation_id=request.organisation_id)

    #         # Get sources from organisation service
    #         sources = self.organisation_service.list_sources(request.organisation_id)

    #         source_list = []
    #         for source in sources:
    #             source_info = connector_pb2.SourceInfo(
    #                 source_id=source.get("id", ""),
    #                 connector_type=source.get("connector_type", ""),
    #                 name=source.get("name", ""),
    #                 status=source.get("status", "inactive"),
    #                 created_at=source.get("created_at", ""),
    #                 last_sync=source.get("last_sync", ""),
    #                 properties=convert_properties_to_string_map(
    #                     source.get("properties", {})
    #                 ),
    #             )
    #             source_list.append(source_info)

    #         return connector_pb2.ListSourcesResponse(
    #             success=True, sources=source_list, total_count=len(source_list)
    #         )

    #     except Exception as e:
    #         logger.error("Error listing sources", error=str(e))
    #         context.set_code(grpc.StatusCode.INTERNAL)
    #         context.set_details(f"Internal error: {str(e)}")
    #         return connector_pb2.ListSourcesResponse(
    #             success=False, message=f"Error listing sources: {str(e)}"
    #         )

    def listSources(self, request, context):
        """List sources for an organisation"""
        try:
            logger.info("Listing sources", organisation_id=request.organisation_id)

            # Get sources from organisation service
            sources = self.source_service.listSources(request.organisation_id)
            # print("connector service: ", sources)
            # source_list = []
            # for source in sources:
            #     source_info = {
            #         "id":source.get("id", ""),
            #         "organisation_id":request.organisation_id,
            #         "connector_type":source.get("connector_type", ""),
            #         "name":source.get("name", ""),
            #         "status":source.get("status", "inactive"),
            #         "created_at":source.get("created_at", ""),
            #         "last_sync":source.get("last_sync", ""),
            #         "metadata":convert_properties_to_string_map(
            #             source.get("properties", {})
            #         ),
            #     }
            #     source_list.append(source_info)

            return connector_pb2.ListSourcesResponse(
                success=True, message="Sources retrieved successfully", sources=sources
            )

        except Exception as e:
            logger.error("Error listing sources", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return connector_pb2.ListSourcesResponse(
                success=False, message=f"Error listing sources: {str(e)}"
            )

    def deleteSource(self, request, context):
        """Delete a source"""
        try:
            logger.info("Deleting source", source_id=request.source_id)

            result = self.organisation_service.delete_source(request.source_id)

            return connector_pb2.DeleteSourceResponse(
                success=result.get("success", False), message=result.get("message", "")
            )

        except Exception as e:
            logger.error("Error deleting source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return connector_pb2.DeleteSourceResponse(
                success=False, message=f"Error deleting source: {str(e)}"
            )

    def syncSource(self, request, context):
        """Sync a source"""
        try:
            logger.info("Syncing source: ", source_id=request.source_id)

            # Get source info to determine connector type
            source_result = self.source_service.getSourceById(request.source_id)

            if not source_result:
                return connector_pb2.SyncSourceResponse(
                    success=False, message="Source not found"
                )

            source_data = source_result["s"]
            organisation_id = source_result["org_id"]
            source = {
                "connector_type": source_data.get("type", "").lower(),
            }
            print("\n\n source_data: \n\n", source_data)

            if source["connector_type"] == "google_drive":
                # Commenting out the existing Redis-based sync worker
                # result = self.sync_worker.sync_source(
                #     source_id=request.source_id, full_sync=request.full_sync
                # )
                logger.info(
                    "Pushing sync_drive_task: ",
                    organisation_id=organisation_id,
                    user_id=request.user_id,
                    full_sync=request.full_sync,
                    source_id=request.source_id,
                )
                # Directly calling the Celery task
                task = sync_drive_task.delay(
                    organisation_id=source_data.get("organisation_id"),
                    user_id=request.user_id,
                    full_sync=request.full_sync,
                )

                return connector_pb2.SyncSourceResponse(
                    success=True,
                    message="Sync task has been queued successfully.",
                    sync_id=task.id,
                )
            else:
                return connector_pb2.SyncSourceResponse(
                    success=False,
                    message=f"Unsupported connector type for sync: {source.get('connector_type')}",
                )

        except Exception as e:
            logger.error("Error syncing source", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return connector_pb2.SyncSourceResponse(
                success=False, message=f"Error syncing source: {str(e)}"
            )

    def listFiles(self, request, context):
        """List files from a source"""
        try:
            logger.info("Listing files", source_id=request.source_id)

            # Get source info to determine connector type
            source = self.organisation_service.get_source(request.source_id)

            if source.get("connector_type") == "google_drive":
                files = self.google_drive_service.list_files(
                    source_id=request.source_id,
                    folder_id=request.folder_id if request.folder_id else None,
                    page_size=request.page_size if request.page_size else 100,
                    page_token=request.page_token if request.page_token else None,
                )

                file_list = []
                for file_data in files.get("files", []):
                    file_model = connector_pb2.DriveFileModel(
                        id=file_data.get("id", ""),
                        name=file_data.get("name", ""),
                        mime_type=file_data.get("mimeType", ""),
                        size=str(file_data.get("size", 0)),
                        created_time=file_data.get("createdTime", ""),
                        modified_time=file_data.get("modifiedTime", ""),
                        web_view_link=file_data.get("webViewLink", ""),
                        properties=convert_properties_to_string_map(
                            file_data.get("properties", {})
                        ),
                    )
                    file_list.append(file_model)

                return connector_pb2.ListFilesResponse(
                    success=True,
                    files=file_list,
                    next_page_token=files.get("nextPageToken", ""),
                    total_count=len(file_list),
                )
            else:
                return connector_pb2.ListFilesResponse(
                    success=False,
                    message=f"Unsupported connector type: {source.get('connector_type')}",
                )

        except Exception as e:
            logger.error("Error listing files", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return connector_pb2.ListFilesResponse(
                success=False, message=f"Error listing files: {str(e)}"
            )

    def syncFileByUrl(self, request, context):
        """Sync a file by URL"""
        try:
            logger.info("Syncing file by URL", drive_url=request.drive_url)

            result = self.google_drive_service.sync_file_by_url(
                drive_url=request.drive_url,
                agent_id=request.agent_id,
                user_id=request.user_id if request.user_id else None,
                organisation_id=request.organisation_id,
            )
            return result
            return connector_pb2.SyncFileByUrlResponse(
                success=result.get("success", False),
                message=result.get("message", ""),
                file_id=result.get("file_id", ""),
                file_name=result.get("file_name", ""),
                sync_status=result.get("sync_status", "failed"),
            )

        except Exception as e:
            logger.error("Error syncing file by URL", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal error: {str(e)}")
            return connector_pb2.SyncFileByUrlResponse(
                success=False, message=f"Error syncing file: {str(e)}"
            )

    # Add other connector methods as needed...
    def updateSourceCredentials(self, request, context):
        """Update source credentials"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def validateSource(self, request, context):
        """Validate a source"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def getConnectorInfo(self, request, context):
        """Get connector information"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def listConnectorTypes(self, request, context):
        """List available connector types"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def testConnection(self, request, context):
        """Test connection to a source"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def getSyncStatus(self, request, context):
        """Get sync status"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def disconnectDrive(self, request, context):
        """Disconnect drive"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def getFileDetails(self, request, context):
        """Get file details"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def getFolderById(self, request, context):
        """Get folder by ID"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def syncFolderByIds(self, request, context):
        """Sync folder by IDs"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def checkFileAccess(self, request, context):
        """Check file access"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def listTopLevelFolders(self, request, context):
        """List top level folders"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")
