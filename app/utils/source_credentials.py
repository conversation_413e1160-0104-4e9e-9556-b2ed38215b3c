import json
import os
import tempfile
from typing import Tuple

import structlog

from app.services.neo4j_service import execute_read_query
from app.utils.constants.sources import SourceType

logger = structlog.get_logger()


def get_source_credentials(organisation_id, source_type=None, credential_type="oauth"):
    """
    Retrieve credentials from a Source node.

    Args:
        organisation_id: The ID of the organization
        source_type: The type of source (default: google_drive)
        credential_type: Type of credentials to retrieve ('oauth' or 'service_account')

    Returns:
        The credentials JSON string or None if not found
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value

    # For all source types, we use key field as a generic key
    # This field stores different types of credentials based on the source type:
    # - For Google Drive: Service account JSON
    # - For Jira: API key
    if credential_type == "service_account":
        field = "s.key"
    else:
        field = "s.oauth_credentials"

    query = f"""
    MATCH (o:Organisation {{id: $org_id}})-[:HAS_SOURCE]->(s:Source)
    WHERE s.type = $source_type
    RETURN {field}
    """

    params = {"org_id": organisation_id, "source_type": source_type}

    result = execute_read_query(query, params)
    print("\n result: \n", result)
    if not result or not result[0].get(field):
        logger.error(
            f"No {credential_type} credentials found for organization {organisation_id}"
        )
        return None

    return result[0][field]


def get_service_account_credentials(
    organisation_id: str, source_type: str = None
) -> Tuple[bool, str]:
    """
    Get service account credentials or API key for an organisation.

    Args:
        organisation_id: Organisation ID
        source_type: Source type (default: google_drive)

    Returns:
        Tuple of (success, credentials_json_or_error_message)
    """
    if source_type is None:
        source_type = SourceType.GOOGLE_DRIVE.value

    credentials_json = get_source_credentials(
        organisation_id, source_type, "service_account"
    )
    if not credentials_json:
        return False, f"No authentication key found for organization {organisation_id}"

    return True, credentials_json
