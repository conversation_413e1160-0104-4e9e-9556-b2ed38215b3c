"""
Redis database connection module.
"""
import redis
from typing import Optional
from app.core.config import settings


class RedisConnectionManager:
    """
    Manages a singleton Redis connection.
    """
    _client: Optional[redis.Redis] = None

    @classmethod
    def get_client(cls) -> redis.Redis:
        """
        Returns a singleton instance of the Redis client.
        
        Returns:
            redis.Redis: Redis client instance
        """
        if cls._client is None:
            cls._client = redis.Redis.from_url(
                settings.REDIS_URI, 
                decode_responses=True,
                health_check_interval=30  # Add this line
            )
        return cls._client

    @classmethod
    def close_client(cls) -> None:
        """
        Closes the Redis client connection.
        """
        if cls._client is not None:
            cls._client.close()
            cls._client = None


def get_redis_client() -> redis.Redis:
    """
    Get Redis client instance.
    
    Returns:
        redis.Redis: Redis client instance
    """
    return RedisConnectionManager.get_client()