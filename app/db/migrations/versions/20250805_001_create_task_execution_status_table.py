"""create task_execution_status table"""

import sqlalchemy as sa
import sqlalchemy.dialects.postgresql as pg
from alembic import op

# revision identifiers, used by Alembic.
revision = "20250805_001"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "task_execution_status",
        sa.Column("id", pg.UUID(as_uuid=True), primary_key=True),
        sa.Column("task_name", sa.String(), nullable=False),
        sa.Column("task_type", sa.String(), nullable=True),
        sa.Column("service_type", sa.String(), nullable=True),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING", "IN_PROGRESS", "COMPLETED", "FAILED", name="taskstatusenum"
            ),
            nullable=False,
        ),
        sa.Column("progress", sa.Integer(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("celery_task_id", sa.String(), nullable=True),
        sa.Column("organisation_id", pg.UUID(as_uuid=True), nullable=True),
        sa.Column("user_id", pg.UUID(as_uuid=True), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
    )


def downgrade():
    op.drop_table("task_execution_status")
    op.execute("DROP TYPE IF EXISTS taskstatusenum")
