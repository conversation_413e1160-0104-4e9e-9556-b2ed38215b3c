"""
Task Status Service

This service provides a simplified interface for managing and tracking the
status of Celery tasks. It is designed to be used by gRPC services or
other background processes that need to report task state without being
tightly coupled to the underlying database implementation.

Key Features:
- Create a new task execution record.
- Update the status and progress of an existing task.
- Mark a task as failed with an error message.
- Abstract the database session management for easy integration.
"""

from contextlib import contextmanager

from sqlalchemy.orm import Session

from app.db.postgres import get_db
from app.modules.tasks.models.task_status import TaskExecutionStatus, TaskStatusEnum


@contextmanager
def get_task_status_service():
    """
    Provides a transactional database session for the task status service.
    """
    db = next(get_db())
    try:
        yield TaskStatusService(db)
        db.commit()
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()


class TaskStatusService:
    """
    Manages task execution status in the database.
    """

    def __init__(self, db: Session):
        self.db = db

    def create_task(
        self,
        celery_task_id: str,
        task_name: str,
        service_type: str,
        task_type: str,
        organisation_id: str = None,
        user_id: str = None,
    ) -> TaskExecutionStatus:
        """
        Creates a new task execution record in a PENDING state.

        Args:
            celery_task_id: The unique ID of the Celery task.
            task_name: A descriptive name for the task.
            service_type: The type of service (e.g., 'GDRIVE', 'JIRA').
            task_type: The type of task (e.g., 'SYNC', 'DELETE').
            organisation_id: The ID of the organization, if applicable.
            user_id: The ID of the user who initiated the task, if applicable.

        Returns:
            The newly created TaskExecution object.
        """
        task = TaskExecutionStatus(
            celery_task_id=celery_task_id,
            task_name=task_name,
            service_type=service_type,
            task_type=task_type,
            status=TaskStatusEnum.PENDING,
            organisation_id=organisation_id,
            user_id=user_id,
        )
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        return task

    def update_task_status(
        self, celery_task_id: str, status: TaskStatusEnum, progress: int = None
    ):
        """
        Updates the status and progress of a task.

        Args:
            celery_task_id: The ID of the Celery task.
            status: The new status (e.g., IN_PROGRESS, SUCCESS).
            progress: The current progress percentage (0-100).
        """
        task = (
            self.db.query(TaskExecutionStatus)
            .filter(TaskExecutionStatus.celery_task_id == celery_task_id)
            .first()
        )
        if task:
            task.status = status.value
            if progress is not None:
                task.progress = progress
            self.db.commit()

    def mark_as_failed(self, celery_task_id: str, error_message: str):
        """
        Marks a task as FAILED and records the error message.

        Args:
            celery_task_id: The ID of the Celery task.
            error_message: The reason for the failure.
        """
        task = (
            self.db.query(TaskExecutionStatus)
            .filter(TaskExecutionStatus.celery_task_id == celery_task_id)
            .first()
        )
        if task:
            task.status = TaskStatusEnum.FAILED
            task.error_message = error_message
            self.db.commit()
