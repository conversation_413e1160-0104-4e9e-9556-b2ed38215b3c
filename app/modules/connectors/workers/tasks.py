"""
Celery tasks for Google Drive integration.
"""

from concurrent.futures import ThreadPoolExecutor

from app.modules.connectors.handlers.gdrive.services.google_drive_service import (
    GoogleDriveService,
)
from app.modules.connectors.workers.celery_worker import app


@app.task
def sync_folders_by_ids(folder_ids):
    """
    Synchronizes a list of Google Drive folders by their IDs.

    Args:
        folder_ids (list): A list of Google Drive folder IDs to synchronize.
    """
    with ThreadPoolExecutor(max_workers=4) as executor:
        # sync_folder_by_id is not defined; placeholder logic
        results = [f"Simulated sync for folder {fid}" for fid in folder_ids]
    return results


from app.modules.connectors.services.task_status_service import (
    TaskStatusService,
    get_task_status_service,
)
from app.modules.tasks.models.task_status import TaskStatusEnum


@app.task(bind=True)
def sync_drive_task(self, organisation_id: str, user_id: str, full_sync: bool = False):
    """
    Synchronizes a Google Drive for a given organization, with status tracking.

    This Celery task now uses the TaskStatusService to provide real-time
    updates on its progress. It creates a task record upon starting and
    updates it as it progresses, or marks it as failed if an error occurs.

    Args:
        organisation_id: The ID of the organization to sync.
        user_id: The ID of the user initiating the sync.
        full_sync: Whether to perform a full sync.
    """
    task_id = self.request.id
    with get_task_status_service() as service:
        try:
            # Create a new task record in PENDING state
            service.create_task(
                celery_task_id=task_id,
                task_name="Google Drive Sync",
                service_type="GDRIVE",
                task_type="SYNC",
                organisation_id=organisation_id,
                user_id=user_id,
            )

            # Update status to IN_PROGRESS
            service.update_task_status(task_id, TaskStatusEnum.IN_PROGRESS, progress=10)

            # Simulate the main work of the task
            drive_service = GoogleDriveService()
            drive_service.sync_drive(organisation_id, full_sync)

            # Update status to SUCCESS
            service.update_task_status(task_id, TaskStatusEnum.COMPLETED, progress=100)

        except Exception as e:
            # Mark the task as FAILED
            service.mark_as_failed(task_id, error_message=str(e))
            # Optional: re-raise the exception if you want Celery to record it as a failure
            raise


@app.task
def sync_file_by_id_task(file_id, agent_id, user_id, organisation_id, url=None):
    """
    Synchronizes a single file by its ID.

    Args:
        file_id (str): The ID of the file to synchronize.
        agent_id (str): The ID of the agent initiating the sync.
        user_id (str): The ID of the user who owns the file.
        organisation_id (str): The ID of the organization.
        url (str, optional): The URL of the file. Defaults to None.
    """
    service = GoogleDriveService()
    return service.sync_file_by_id(file_id, agent_id, user_id, organisation_id, url)


@app.task
def sync_folder_recursively_task(organisation_id, folder_id):
    """
    Recursively synchronizes a folder and its contents.

    Args:
        organisation_id (str): The ID of the organization.
        folder_id (str): The ID of the folder to synchronize.
    """
    service = GoogleDriveService()
    drive = service.get_service_account_drive_service(organisation_id)
    return service.sync_folder_recursively_with_permissions(
        drive, organisation_id, folder_id
    )
