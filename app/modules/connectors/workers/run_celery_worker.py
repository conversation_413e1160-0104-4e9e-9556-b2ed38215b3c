#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the Ce<PERSON>y worker for Google Drive sync tasks.
"""

import os
import sys
import argparse
import structlog
from pathlib import Path

# Add the parent directory to sys.path to allow importing app modules
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent.parent))

from app.modules.connectors.workers.celery_worker import app

logger = structlog.get_logger()

def parse_args():
    parser = argparse.ArgumentParser(description='Run Celery worker for Google Drive sync tasks.')
    parser.add_argument('--loglevel', default='info', help='Logging level (e.g., debug, info, warning, error, critical)')
    parser.add_argument('--concurrency', type=int, default=4, help='Number of worker processes to run concurrently')
    return parser.parse_args()

def main():
    args = parse_args()
    
    logger.info(f"Starting Celery worker with log level {args.loglevel} and concurrency {args.concurrency}")
    
    # Start the Celery worker
    app.worker_main(argv=['worker', f'--loglevel={args.loglevel}', f'--concurrency={args.concurrency}'])

if __name__ == '__main__':
    main()
